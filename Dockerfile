# Android Master Token Authentication - Docker Container
# ====================================================

FROM python:3.11-slim-bullseye

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies with specific versions for compatibility
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY android_master_token_auth.py .
COPY test_authentication.py .

# Create a non-root user for security
RUN useradd -m -u 1000 tokenuser && chown -R tokenuser:tokenuser /app
USER tokenuser

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Default command
CMD ["python", "android_master_token_auth.py"]

# Labels for metadata
LABEL maintainer="AI Assistant"
LABEL description="Android Master Token Authentication Container"
LABEL version="1.0"
