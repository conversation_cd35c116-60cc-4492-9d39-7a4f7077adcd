#!/usr/bin/env python3
"""
Example Usage of Android Master Tokens
======================================

This script demonstrates how to use the obtained Android Master Tokens
for various Google services and API calls.

Author: AI Assistant
License: MIT
"""

import json
import os
import requests
from typing import Optional, Dict, Any

class GoogleServiceClient:
    """Client for making authenticated requests to Google services"""
    
    def __init__(self, tokens_file: str = "google_tokens.json"):
        """
        Initialize client with saved tokens
        
        Args:
            tokens_file: Path to the JSON file containing tokens
        """
        self.tokens = self._load_tokens(tokens_file)
        self.master_token = self.tokens.get('master_token')
        self.access_token = self.tokens.get('access_token')
        self.android_id = self.tokens.get('android_id')
        
    def _load_tokens(self, tokens_file: str) -> Dict[str, Any]:
        """Load tokens from JSON file"""
        if not os.path.exists(tokens_file):
            raise FileNotFoundError(f"Token file {tokens_file} not found. Run the authentication script first.")
        
        with open(tokens_file, 'r') as f:
            tokens = json.load(f)
        
        if not tokens.get('master_token'):
            raise ValueError("No master token found in token file")
        
        return tokens
    
    def refresh_access_token(self) -> Optional[str]:
        """
        Refresh the access token using the master token
        
        Returns:
            New access token or None if failed
        """
        try:
            from gpsoauth import perform_oauth
            
            res = perform_oauth(
                self.tokens['username'],
                self.master_token,
                self.android_id,
                app='com.google.android.apps.chromecast.app',
                service='oauth2:https://www.google.com/accounts/OAuthLogin',
                client_sig='24bb24c05e47e0aefa68a58a766179d9b613a600'
            )
            
            if 'Auth' in res:
                self.access_token = res['Auth']
                
                # Update the tokens file
                self.tokens['access_token'] = self.access_token
                with open('google_tokens.json', 'w') as f:
                    json.dump(self.tokens, f, indent=2)
                
                print("[✓] Access token refreshed successfully")
                return self.access_token
            else:
                print(f"[!] Failed to refresh access token: {res}")
                return None
                
        except Exception as e:
            print(f"[!] Error refreshing access token: {e}")
            return None
    
    def get_google_devices(self) -> Optional[Dict[str, Any]]:
        """
        Get Google Home devices using glocaltokens
        
        Returns:
            Dictionary of devices or None if failed
        """
        try:
            from glocaltokens.client import GLocalAuthenticationTokens
            
            client = GLocalAuthenticationTokens(
                username=self.tokens['username'],
                master_token=self.master_token
            )
            
            devices = client.get_google_devices_json()
            print(f"[✓] Found {len(devices)} Google devices")
            return devices
            
        except Exception as e:
            print(f"[!] Error getting Google devices: {e}")
            return None
    
    def make_authenticated_request(self, url: str, headers: Optional[Dict[str, str]] = None) -> Optional[requests.Response]:
        """
        Make an authenticated request to a Google API
        
        Args:
            url: API endpoint URL
            headers: Additional headers
            
        Returns:
            Response object or None if failed
        """
        if not self.access_token:
            print("[!] No access token available. Trying to refresh...")
            if not self.refresh_access_token():
                return None
        
        auth_headers = {
            'Authorization': f'Bearer {self.access_token}',
            'User-Agent': 'Android-GCM/1.5 (generic_x86 KK)',
            'X-Android-ID': self.android_id
        }
        
        if headers:
            auth_headers.update(headers)
        
        try:
            response = requests.get(url, headers=auth_headers)
            
            if response.status_code == 401:
                print("[!] Access token expired. Refreshing...")
                if self.refresh_access_token():
                    auth_headers['Authorization'] = f'Bearer {self.access_token}'
                    response = requests.get(url, headers=auth_headers)
            
            return response
            
        except Exception as e:
            print(f"[!] Error making authenticated request: {e}")
            return None
    
    def test_authentication(self) -> bool:
        """
        Test if the authentication tokens are working
        
        Returns:
            True if authentication is working, False otherwise
        """
        print("[*] Testing authentication...")
        
        # Test 1: Check token format
        if not self.master_token.startswith('aas_et/'):
            print("[!] Invalid master token format")
            return False
        
        print("[✓] Master token format is valid")
        
        # Test 2: Try to refresh access token
        if not self.refresh_access_token():
            print("[!] Cannot refresh access token")
            return False
        
        print("[✓] Access token refresh successful")
        
        # Test 3: Try to get Google devices
        devices = self.get_google_devices()
        if devices is None:
            print("[!] Cannot retrieve Google devices")
            return False
        
        print("[✓] Google devices retrieval successful")
        
        return True
    
    def display_token_info(self):
        """Display information about the loaded tokens"""
        print("\nToken Information:")
        print("=" * 40)
        print(f"Username: {self.tokens.get('username', 'N/A')}")
        print(f"Android ID: {self.android_id}")
        print(f"Master Token: {self.master_token[:30]}..." if self.master_token else "N/A")
        print(f"Access Token: {self.access_token[:30]}..." if self.access_token else "N/A")
        
        # Token age
        import time
        timestamp = self.tokens.get('timestamp', 0)
        age_hours = (time.time() - timestamp) / 3600
        print(f"Token Age: {age_hours:.1f} hours")
        print("=" * 40)


def main():
    """Main example function"""
    print("Android Master Token - Example Usage")
    print("=" * 40)
    
    try:
        # Initialize client with saved tokens
        client = GoogleServiceClient()
        
        # Display token information
        client.display_token_info()
        
        # Test authentication
        if client.test_authentication():
            print("\n[✓] Authentication test passed!")
            
            # Example: Get Google devices
            print("\n[*] Retrieving Google devices...")
            devices = client.get_google_devices()
            
            if devices:
                print(f"\nFound {len(devices)} devices:")
                for i, device in enumerate(devices, 1):
                    name = device.get('device_name', 'Unknown')
                    model = device.get('hardware', 'Unknown')
                    print(f"  {i}. {name} ({model})")
            
            # Example: Make a custom authenticated request
            print("\n[*] Example: Making authenticated request...")
            # Note: Replace with actual Google API endpoint as needed
            # response = client.make_authenticated_request("https://api.google.com/some/endpoint")
            
            print("\n[✓] Example completed successfully!")
            
        else:
            print("\n[!] Authentication test failed!")
            
    except FileNotFoundError:
        print("[!] Token file not found. Please run the authentication script first:")
        print("    python android_master_token_auth.py")
        
    except Exception as e:
        print(f"[!] Error: {e}")


if __name__ == "__main__":
    main()
