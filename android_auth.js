#!/usr/bin/env node
/**
 * Android Master Token Authentication - Node.js Version
 * ====================================================
 * 
 * This Node.js implementation sometimes works better with Google's authentication
 * than Python versions due to different HTTP handling.
 */

const https = require('https');
const crypto = require('crypto');
const querystring = require('querystring');
const os = require('os');

// Configuration
const USERNAME = '<EMAIL>';
const PASSWORD = 'bppjpvwspocrwhqs';
const DEBUG = true;

// Android device simulation
function generateAndroidId() {
    // Generate a realistic Android device ID
    const seed = `${USERNAME}_android_device_${Date.now()}`;
    const hash = crypto.createHash('md5').update(seed).digest('hex');
    return hash.substring(0, 16).toUpperCase();
}

function getNetworkInterfaces() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const iface of interfaces[name]) {
            if (iface.family === 'IPv4' && !iface.internal) {
                return iface.mac.replace(/:/g, '').toUpperCase();
            }
        }
    }
    return generateAndroidId();
}

// HTTP request helper
function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: data
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (postData) {
            req.write(postData);
        }
        req.end();
    });
}

// Parse response data
function parseResponse(body) {
    const result = {};
    const lines = body.split('\n');
    
    for (const line of lines) {
        if (line.includes('=')) {
            const [key, value] = line.split('=', 2);
            result[key] = value;
        }
    }
    
    return result;
}

// Get master token
async function getMasterToken(username, password, androidId) {
    console.log(`[*] Attempting master login for ${username}`);
    console.log(`[*] Using Android ID: ${androidId}`);
    
    const postData = querystring.stringify({
        'accountType': 'HOSTED_OR_GOOGLE',
        'Email': username,
        'Passwd': password,
        'service': 'ac2dm',
        'source': 'android',
        'androidId': androidId,
        'device_country': 'us',
        'operatorCountry': 'us',
        'lang': 'en',
        'sdk_version': '17',
        'client_sig': '38918a453d07199354f8b19af05ec6562ced5788',
        'callerSig': '38918a453d07199354f8b19af05ec6562ced5788',
        'droidguard_results': 'dummy123'
    });

    const options = {
        hostname: 'android.clients.google.com',
        port: 443,
        path: '/auth',
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Content-Length': Buffer.byteLength(postData),
            'User-Agent': 'GoogleAuth/1.4 (generic_x86 KK)',
            'Accept-Encoding': 'gzip'
        }
    };

    try {
        const response = await makeRequest(options, postData);
        
        if (DEBUG) {
            console.log(`[*] Response status: ${response.statusCode}`);
            console.log(`[*] Response body: ${response.body}`);
        }

        const result = parseResponse(response.body);
        
        if (result.Auth) {
            const masterToken = result.Auth;
            if (masterToken.startsWith('aas_et/')) {
                console.log('[✓] Successfully obtained master token!');
                return masterToken;
            } else {
                console.log(`[!] Invalid token format: ${masterToken}`);
                return null;
            }
        } else if (result.Error) {
            console.log(`[!] Authentication error: ${result.Error}`);
            
            if (result.Error === 'BadAuthentication') {
                console.log('[!] Invalid credentials. Check:');
                console.log('    1. Email address is correct');
                console.log('    2. App password is correct (no spaces)');
                console.log('    3. 2FA is enabled on your Google account');
            } else if (result.Error === 'NeedsBrowser') {
                console.log('[!] Browser authentication required.');
                console.log('    Try using an app-specific password.');
            }
            
            return null;
        } else {
            console.log('[!] Unexpected response format');
            return null;
        }
    } catch (error) {
        console.log(`[!] Request failed: ${error.message}`);
        return null;
    }
}

// Get access token
async function getAccessToken(username, masterToken, androidId) {
    console.log('[*] Getting access token...');
    
    const postData = querystring.stringify({
        'accountType': 'HOSTED_OR_GOOGLE',
        'Email': username,
        'EncryptedPasswd': masterToken,
        'service': 'oauth2:https://www.google.com/accounts/OAuthLogin',
        'source': 'android',
        'androidId': androidId,
        'app': 'com.google.android.apps.chromecast.app',
        'client_sig': '24bb24c05e47e0aefa68a58a766179d9b613a600',
        'device_country': 'us',
        'operatorCountry': 'us',
        'lang': 'en',
        'sdk_version': '17'
    });

    const options = {
        hostname: 'android.clients.google.com',
        port: 443,
        path: '/auth',
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Content-Length': Buffer.byteLength(postData),
            'User-Agent': 'GoogleAuth/1.4 (generic_x86 KK)',
            'Accept-Encoding': 'gzip'
        }
    };

    try {
        const response = await makeRequest(options, postData);
        const result = parseResponse(response.body);
        
        if (result.Auth) {
            console.log('[✓] Access token obtained successfully!');
            return result.Auth;
        } else {
            console.log(`[!] Could not get access token: ${result.Error || 'Unknown error'}`);
            return null;
        }
    } catch (error) {
        console.log(`[!] Access token request failed: ${error.message}`);
        return null;
    }
}

// Save tokens to file
function saveTokens(masterToken, accessToken, androidId) {
    const fs = require('fs');
    
    const tokens = {
        master_token: masterToken,
        access_token: accessToken,
        username: USERNAME,
        android_id: androidId,
        timestamp: Date.now() / 1000
    };
    
    fs.writeFileSync('google_tokens.json', JSON.stringify(tokens, null, 2));
    console.log('[✓] Tokens saved to google_tokens.json');
}

// Main function
async function main() {
    console.log('Android Master Token Authentication - Node.js Version');
    console.log('=' * 55);
    
    // Generate Android ID
    const androidId = getNetworkInterfaces();
    console.log(`[*] Generated Android ID: ${androidId}`);
    
    console.log(`
This script generates tokens for Google Home Foyer API.

1. Master token - Form: aas_et/*** (long lived)
2. Access token - Form: ya29.*** (1 hour)

Using credentials:
- Email: ${USERNAME}
- App Password: ${'*'.repeat(PASSWORD.length)}
- Android ID: ${androidId}
`);
    
    // Get master token
    const masterToken = await getMasterToken(USERNAME, PASSWORD, androidId);
    
    if (!masterToken) {
        console.log('\n[!] Failed to obtain master token!');
        console.log('\nTroubleshooting:');
        console.log('1. Verify your app password is correct');
        console.log('2. Ensure 2FA is enabled on your Google account');
        console.log('3. Generate a new app-specific password at:');
        console.log('   https://myaccount.google.com/apppasswords');
        process.exit(1);
    }
    
    console.log(`[✓] Master token: ${masterToken}`);
    
    // Get access token
    const accessToken = await getAccessToken(USERNAME, masterToken, androidId);
    
    if (accessToken) {
        console.log(`[✓] Access token: ${accessToken}`);
    } else {
        console.log('[!] Could not get access token (master token should still work)');
    }
    
    // Save tokens
    saveTokens(masterToken, accessToken, androidId);
    
    console.log('\n' + '='.repeat(55));
    console.log('SUCCESS!');
    console.log('='.repeat(55));
    console.log(`Master Token: ${masterToken}`);
    if (accessToken) {
        console.log(`Access Token: ${accessToken}`);
    }
    console.log(`Android ID: ${androidId}`);
    console.log('='.repeat(55));
    console.log('\n[*] Done.');
}

// Run the script
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { getMasterToken, getAccessToken };
