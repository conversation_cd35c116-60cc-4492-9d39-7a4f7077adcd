# Android Master Token Authentication

A comprehensive Python script to authenticate with Google services and retrieve Android Master Tokens (starting with `aas_et/`) using modern, working methods.

## Features

- **Multiple Authentication Methods**: Uses both `glocaltokens` and `gpsoauth` libraries for maximum compatibility
- **Automatic Dependency Management**: Installs required packages automatically
- **SSL Certificate Handling**: Resolves common SSL verification issues
- **Android Device Emulation**: Generates proper Android device IDs and headers
- **Comprehensive Error Handling**: Provides specific troubleshooting guidance
- **Token Storage**: Saves tokens to JSON file for later use
- **Modern & Updated**: Uses latest working versions of authentication libraries

## Requirements

- Python 3.9 or higher
- Internet connection
- Valid Google account
- App-specific password (recommended)

## Quick Start

### Method 1: Automated Setup (Recommended)

1. Download all files to a directory
2. Run the setup script:

```bash
python setup_and_run.py
```

This will automatically:
- Check Python version compatibility
- Install all required dependencies
- Run the authentication script
- Guide you through the process

### Method 2: Manual Setup

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Run the main script:

```bash
python android_master_token_auth.py
```

## Google Account Setup

### Creating an App-Specific Password (Recommended)

1. Go to [Google App Passwords](https://myaccount.google.com/apppasswords)
2. Sign in to your Google account
3. Select "Mail" as the app
4. Select "Other (custom name)" as the device
5. Enter a name like "Android Token Script"
6. Click "Generate"
7. Copy the 16-character password (remove spaces)
8. Use this password in the script

### Requirements for Your Google Account

- 2-Factor Authentication must be enabled
- Account must have access to Google services
- No recent security issues or restrictions

## Usage

When you run the script, you'll be prompted for:

1. **Google email address**: Your full Gmail address (e.g., `<EMAIL>`)
2. **Password**: Your app-specific password (recommended) or account password

The script will then:

1. Attempt authentication using the `glocaltokens` library
2. Fall back to `gpsoauth` if needed
3. Generate an Android device ID
4. Retrieve your master token (starts with `aas_et/`)
5. Optionally retrieve an access token
6. Save tokens to `google_tokens.json`

## Output

On success, you'll receive:

```
Master Token: aas_et/[long_token_string]
Access Token: ya29.[access_token_string]
Android ID: [device_id]
```

## Token Information

### Master Token (`aas_et/...`)
- Long-lived authentication token
- Used for accessing Google services as an Android device
- Should be stored securely
- May expire after extended periods

### Access Token (`ya29....`)
- Short-lived token (expires in 1 hour)
- Used for immediate API access
- Can be regenerated using the master token

## Troubleshooting

### Common Issues

1. **"BadAuthentication" Error**
   - Use an app-specific password instead of your main password
   - Ensure 2FA is enabled on your Google account
   - Check username and password are correct

2. **"NeedsBrowser" Error**
   - Generate and use an app-specific password
   - Visit [Google App Passwords](https://myaccount.google.com/apppasswords)

3. **SSL Certificate Errors**
   - The script automatically downloads SSL certificates
   - Ensure you have internet access
   - Try running the script as administrator if needed

4. **Import Errors**
   - Run `python setup_and_run.py` to install dependencies
   - Ensure Python 3.9+ is installed
   - Try upgrading pip: `python -m pip install --upgrade pip`

### Manual Dependency Installation

If automatic installation fails:

```bash
pip install glocaltokens>=0.7.6
pip install gpsoauth==1.0.2
pip install "urllib3<2.0.0"
pip install requests>=2.25.0
```

## Security Notes

- **Never share your master token** - it provides access to your Google account
- **Use app-specific passwords** instead of your main Google password
- **Store tokens securely** - treat them like passwords
- **Regenerate tokens periodically** for security
- **Revoke app passwords** when no longer needed

## File Structure

```
android_master_token_auth.py    # Main authentication script
setup_and_run.py               # Automated setup and execution
requirements.txt               # Python dependencies
README.md                      # This documentation
google_tokens.json            # Generated token storage (after running)
cacert.pem                    # SSL certificates (auto-downloaded)
```

## Advanced Usage

### Using Existing Master Token

If you already have a master token, you can modify the script to skip authentication:

```python
auth_client = AndroidMasterTokenAuth(username, password)
auth_client.master_token = "your_existing_master_token"
```

### Custom Android ID

To use a specific Android device ID:

```python
auth_client = AndroidMasterTokenAuth(username, password, android_id="your_device_id")
```

## Docker Usage (Alternative)

For users who prefer containerized execution:

### Build and Run with Docker

```bash
# Build the container
docker build -t android-token-auth .

# Run interactively
docker run -it --rm -v $(pwd)/tokens:/app/tokens android-token-auth
```

### Using Docker Compose

```bash
# Run the authentication script
docker-compose up android-token-auth

# Run tests
docker-compose --profile test up test-runner
```

### Environment Variables (Docker)

You can set credentials via environment variables:

```bash
docker run -it --rm \
  -e GOOGLE_USERNAME=<EMAIL> \
  -e GOOGLE_PASSWORD=your_app_password \
  -v $(pwd)/tokens:/app/tokens \
  android-token-auth
```

## Testing

Run the test suite to verify everything is working:

```bash
python test_authentication.py
```

This will check:
- Dependency availability
- SSL certificate setup
- Basic functionality
- Saved token validation

## License

MIT License - Feel free to use and modify as needed.

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Run the test suite: `python test_authentication.py`
3. Ensure you're using the latest version of the script
4. Verify your Google account setup
5. Try generating a new app-specific password

## Changelog

- **v1.0**: Initial release with dual authentication methods
- Supports both glocaltokens and gpsoauth libraries
- Automatic SSL certificate handling
- Comprehensive error handling and troubleshooting
- Docker support for containerized execution
- Test suite for validation
