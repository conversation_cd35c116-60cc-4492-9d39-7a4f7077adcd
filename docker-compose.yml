# Docker Compose for Android Master Token Authentication
# ====================================================

version: '3.8'

services:
  android-token-auth:
    build: .
    container_name: android_master_token_auth
    stdin_open: true
    tty: true
    volumes:
      # Mount current directory to persist tokens
      - ./tokens:/app/tokens
    environment:
      # Optional: Set credentials via environment variables
      # GOOGLE_USERNAME: <EMAIL>
      # GOOGLE_PASSWORD: your_app_password
      - PYTHONUNBUFFERED=1
    networks:
      - token-network

  # Optional: Test container
  test-runner:
    build: .
    container_name: android_token_test
    command: python test_authentication.py
    volumes:
      - ./tokens:/app/tokens
    networks:
      - token-network
    profiles:
      - test

networks:
  token-network:
    driver: bridge

volumes:
  tokens:
    driver: local
