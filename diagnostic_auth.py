#!/usr/bin/env python3
"""
Diagnostic Android Authentication
=================================

This script provides comprehensive diagnostics and multiple authentication
methods to help identify and resolve authentication issues.
"""

import requests
import subprocess
import sys
import os
import json
import time
from urllib.parse import urlencode

# Configuration
USERNAME = '<EMAIL>'
PASSWORD = 'bppjpvwspocrwhqs'

def check_account_status():
    """Check basic account accessibility"""
    print("[*] Checking account status...")
    
    # Try a basic Google API call to see if the account is accessible
    try:
        response = requests.get('https://accounts.google.com/.well-known/openid_configuration', timeout=10)
        if response.status_code == 200:
            print("[✓] Google services are accessible")
        else:
            print(f"[!] Google services returned status: {response.status_code}")
    except Exception as e:
        print(f"[!] Cannot reach Google services: {e}")

def check_app_password_format():
    """Check if the app password has the correct format"""
    print("[*] Checking app password format...")
    
    # Remove any spaces or special characters
    clean_password = PASSWORD.replace(' ', '').replace('-', '')
    
    print(f"[*] Original password length: {len(PASSWORD)}")
    print(f"[*] Cleaned password length: {len(clean_password)}")
    
    if len(clean_password) == 16:
        print("[✓] App password length looks correct (16 characters)")
    else:
        print(f"[!] App password length is unusual: {len(clean_password)} characters")
        print("    Google app passwords are typically 16 characters")
    
    # Check for common issues
    if ' ' in PASSWORD:
        print("[!] Warning: Password contains spaces")
    if '-' in PASSWORD:
        print("[!] Warning: Password contains dashes")
    
    return clean_password

def try_docker_method():
    """Try using the working Docker container method"""
    print("\n[*] Attempting Docker container method...")
    
    try:
        # Check if Docker is available
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            print("[!] Docker is not available")
            return False
        
        print(f"[✓] Docker is available: {result.stdout.strip()}")
        
        # Try to run the working Docker container
        print("[*] Running Docker container for token extraction...")
        
        docker_cmd = [
            'docker', 'run', '--rm', '-it',
            '-e', f'EMAIL_USERNAME={USERNAME}',
            '-e', f'EMAIL_PASSWORD={PASSWORD}',
            'breph/ha-google-home_get-token'
        ]
        
        print(f"[*] Docker command: {' '.join(docker_cmd[:6])} [credentials hidden]")
        
        # Run with timeout
        result = subprocess.run(docker_cmd, 
                              capture_output=True, text=True, timeout=120)
        
        print(f"[*] Docker exit code: {result.returncode}")
        print(f"[*] Docker output:")
        print(result.stdout)
        
        if result.stderr:
            print(f"[*] Docker errors:")
            print(result.stderr)
        
        # Look for master token in output
        if 'aas_et/' in result.stdout:
            print("[✓] Master token found in Docker output!")
            return True
        else:
            print("[!] No master token found in Docker output")
            return False
            
    except subprocess.TimeoutExpired:
        print("[!] Docker command timed out")
        return False
    except FileNotFoundError:
        print("[!] Docker command not found")
        return False
    except Exception as e:
        print(f"[!] Docker method failed: {e}")
        return False

def try_glocaltokens_with_diagnostics():
    """Try glocaltokens with detailed diagnostics"""
    print("\n[*] Attempting glocaltokens with diagnostics...")
    
    try:
        # Install specific version
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 
            'glocaltokens==0.7.6', '--force-reinstall', '--quiet'
        ])
        
        from glocaltokens.client import GLocalAuthenticationTokens
        
        print("[✓] glocaltokens library imported successfully")
        
        # Try with debug mode
        client = GLocalAuthenticationTokens(
            username=USERNAME,
            password=PASSWORD
        )
        
        print("[*] Created GLocalAuthenticationTokens client")
        
        # Try to get master token with error handling
        try:
            master_token = client.get_master_token()
            
            if master_token:
                print(f"[✓] Master token obtained: {master_token}")
                return master_token
            else:
                print("[!] Master token is None")
                return None
                
        except Exception as e:
            print(f"[!] Error getting master token: {e}")
            print(f"[!] Error type: {type(e).__name__}")
            return None
            
    except ImportError as e:
        print(f"[!] Cannot import glocaltokens: {e}")
        return None
    except Exception as e:
        print(f"[!] glocaltokens method failed: {e}")
        return None

def create_manual_test_script():
    """Create a manual test script for user to run"""
    print("\n[*] Creating manual test script...")
    
    script_content = f'''#!/usr/bin/env python3
"""
Manual Test Script for Android Master Token
"""

import subprocess
import sys

# Install exact working versions
print("Installing exact working versions...")
subprocess.check_call([sys.executable, "-m", "pip", "install", "gpsoauth==1.0.2", "--force-reinstall"])
subprocess.check_call([sys.executable, "-m", "pip", "install", "urllib3==1.25.1", "--force-reinstall"])

from gpsoauth import perform_master_login
import hashlib

USERNAME = "{USERNAME}"
PASSWORD = "{PASSWORD}"

# Generate Android ID
seed = f"{{USERNAME}}_android_device"
hash_obj = hashlib.md5(seed.encode())
android_id = hash_obj.hexdigest()[:16].upper()

print(f"Username: {{USERNAME}}")
print(f"Password: {{'*' * len(PASSWORD)}}")
print(f"Android ID: {{android_id}}")

print("\\nAttempting authentication...")
result = perform_master_login(USERNAME, PASSWORD, android_id)
print(f"Result: {{result}}")

if 'Token' in result:
    token = result['Token']
    print(f"\\nSUCCESS!")
    print(f"Master Token: {{token}}")
    
    # Save to file
    import json
    with open('manual_tokens.json', 'w') as f:
        json.dump({{"master_token": token, "android_id": android_id}}, f, indent=2)
    print("\\nTokens saved to manual_tokens.json")
else:
    print(f"\\nFAILED: {{result.get('Error', 'Unknown error')}}")
'''
    
    with open('manual_test.py', 'w') as f:
        f.write(script_content)
    
    print("[✓] Manual test script created: manual_test.py")
    print("    You can run this independently with: python manual_test.py")

def main():
    """Main diagnostic function"""
    print("Diagnostic Android Authentication")
    print("=" * 50)
    print(f"Email: {USERNAME}")
    print(f"Password: {'*' * len(PASSWORD)}")
    print("=" * 50)
    
    # Step 1: Basic checks
    check_account_status()
    clean_password = check_app_password_format()
    
    # Step 2: Try Docker method (most reliable)
    if try_docker_method():
        print("\n[✓] Docker method succeeded! Check the output above for tokens.")
        return
    
    # Step 3: Try glocaltokens with diagnostics
    token = try_glocaltokens_with_diagnostics()
    if token:
        print(f"\n[✓] glocaltokens method succeeded!")
        print(f"Master Token: {token}")
        return
    
    # Step 4: Create manual test script
    create_manual_test_script()
    
    print("\n" + "=" * 50)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 50)
    print("All automated methods failed. Possible issues:")
    print("1. App password may be incorrect or expired")
    print("2. Account may have additional security restrictions")
    print("3. Google may have changed authentication requirements")
    print()
    print("RECOMMENDED ACTIONS:")
    print("1. Generate a NEW app-specific password:")
    print("   https://myaccount.google.com/apppasswords")
    print("2. Ensure 2FA is enabled on your Google account")
    print("3. Try running the manual test script: python manual_test.py")
    print("4. If Docker is available, try the Docker method manually:")
    print("   docker run --rm -it breph/ha-google-home_get-token")
    print("=" * 50)

if __name__ == "__main__":
    main()
