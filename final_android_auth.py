#!/usr/bin/env python3
"""
Final Android Master Token Authentication
=========================================

This is the ultimate solution that combines all working methods and handles
all common issues including SSL certificates, dependency conflicts, and
authentication problems.

Based on extensive research and testing of multiple approaches.
"""

import os
import sys
import subprocess
import urllib.request
import ssl
import json
import time
import hashlib
import requests
from urllib.parse import urlencode

# Configuration
USERNAME = '<EMAIL>'
PASSWORD = 'bppjpvwspocrwhqs'
DEBUG = True

def setup_ssl_environment():
    """Setup SSL certificates and environment"""
    print("[*] Setting up SSL environment...")
    
    try:
        # Download SSL certificates
        cert_path = os.path.join(os.getcwd(), 'cacert.pem')
        if not os.path.exists(cert_path):
            print("[*] Downloading SSL certificates...")
            urllib.request.urlretrieve('https://curl.se/ca/cacert.pem', cert_path)
        
        # Set environment variables
        os.environ['CURL_CA_BUNDLE'] = cert_path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        os.environ['SSL_CERT_FILE'] = cert_path
        
        print(f"[✓] SSL certificates configured: {cert_path}")
        return True
        
    except Exception as e:
        print(f"[!] SSL setup failed: {e}")
        return False

def install_working_dependencies():
    """Install the exact working versions"""
    print("[*] Installing working dependencies...")
    
    # Uninstall conflicting packages
    packages_to_remove = ['glocaltokens', 'gpsoauth', 'urllib3']
    for pkg in packages_to_remove:
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'uninstall', pkg, '-y'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        except:
            pass
    
    # Install exact working versions
    working_packages = [
        'urllib3==1.25.1',
        'gpsoauth==1.0.2',
        'requests>=2.25.0'
    ]
    
    for package in working_packages:
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package, '--force-reinstall'
            ], stdout=subprocess.DEVNULL)
            print(f"[✓] Installed {package}")
        except Exception as e:
            print(f"[!] Failed to install {package}: {e}")
            return False
    
    return True

def generate_realistic_android_id():
    """Generate a realistic Android device ID"""
    # Use a combination of username and timestamp for uniqueness
    seed = f"{USERNAME}_android_device_{int(time.time())}"
    hash_obj = hashlib.md5(seed.encode())
    android_id = hash_obj.hexdigest()[:16].upper()
    return android_id

def method_1_gpsoauth_with_ssl_fix():
    """Method 1: gpsoauth with SSL certificate fix"""
    print("\n[*] Method 1: gpsoauth with SSL fix")
    
    try:
        # Import after installing correct version
        from gpsoauth import perform_master_login, perform_oauth
        
        android_id = generate_realistic_android_id()
        print(f"[*] Using Android ID: {android_id}")
        
        # Disable SSL verification temporarily
        import ssl
        ssl._create_default_https_context = ssl._create_unverified_context
        
        # Attempt authentication
        result = perform_master_login(USERNAME, PASSWORD, android_id)
        
        if DEBUG:
            print(f"[*] Authentication result: {result}")
        
        if 'Token' in result:
            master_token = result['Token']
            if master_token.startswith('aas_et/'):
                print(f"[✓] SUCCESS! Master token: {master_token}")
                
                # Try to get access token
                try:
                    oauth_result = perform_oauth(
                        USERNAME, master_token, android_id,
                        app='com.google.android.apps.chromecast.app',
                        service='oauth2:https://www.google.com/accounts/OAuthLogin',
                        client_sig='24bb24c05e47e0aefa68a58a766179d9b613a600'
                    )
                    
                    if 'Auth' in oauth_result:
                        access_token = oauth_result['Auth']
                        print(f"[✓] Access token: {access_token}")
                        return master_token, access_token, android_id
                    
                except Exception as e:
                    print(f"[!] Access token failed: {e}")
                
                return master_token, None, android_id
            else:
                print(f"[!] Invalid token format: {master_token}")
        else:
            error = result.get('Error', 'Unknown error')
            print(f"[!] Authentication failed: {error}")
        
        return None, None, android_id
        
    except Exception as e:
        print(f"[!] Method 1 failed: {e}")
        return None, None, None

def method_2_direct_requests():
    """Method 2: Direct HTTP requests with custom headers"""
    print("\n[*] Method 2: Direct HTTP requests")
    
    try:
        android_id = generate_realistic_android_id()
        print(f"[*] Using Android ID: {android_id}")
        
        # Create session with SSL disabled
        session = requests.Session()
        session.verify = False
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # Set Android-like headers
        headers = {
            'User-Agent': 'GoogleAuth/1.4 (generic_x86 KK)',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept-Encoding': 'gzip'
        }
        
        # Authentication data
        auth_data = {
            'accountType': 'HOSTED_OR_GOOGLE',
            'Email': USERNAME,
            'Passwd': PASSWORD,
            'service': 'ac2dm',
            'source': 'android',
            'androidId': android_id,
            'device_country': 'us',
            'operatorCountry': 'us',
            'lang': 'en',
            'sdk_version': '17'
        }
        
        response = session.post(
            'https://android.clients.google.com/auth',
            data=auth_data,
            headers=headers,
            timeout=30
        )
        
        if DEBUG:
            print(f"[*] Response status: {response.status_code}")
            print(f"[*] Response text: {response.text[:200]}...")
        
        # Parse response
        result = {}
        for line in response.text.split('\n'):
            if '=' in line:
                key, value = line.split('=', 1)
                result[key] = value
        
        if 'Auth' in result:
            master_token = result['Auth']
            if master_token.startswith('aas_et/'):
                print(f"[✓] SUCCESS! Master token: {master_token}")
                return master_token, None, android_id
        
        print(f"[!] Method 2 failed: {result}")
        return None, None, android_id
        
    except Exception as e:
        print(f"[!] Method 2 failed: {e}")
        return None, None, None

def method_3_subprocess_isolation():
    """Method 3: Run in subprocess with isolated environment"""
    print("\n[*] Method 3: Subprocess isolation")
    
    try:
        android_id = generate_realistic_android_id()
        
        # Create isolated script
        script_content = f'''
import os
import ssl
ssl._create_default_https_context = ssl._create_unverified_context

from gpsoauth import perform_master_login

USERNAME = "{USERNAME}"
PASSWORD = "{PASSWORD}"
ANDROID_ID = "{android_id}"

try:
    result = perform_master_login(USERNAME, PASSWORD, ANDROID_ID)
    if 'Token' in result:
        token = result['Token']
        if token.startswith('aas_et/'):
            print(f"SUCCESS:{{token}}")
        else:
            print(f"INVALID:{{token}}")
    else:
        print(f"ERROR:{{result.get('Error', 'Unknown')}}")
except Exception as e:
    print(f"EXCEPTION:{{e}}")
'''
        
        with open('isolated_auth.py', 'w') as f:
            f.write(script_content)
        
        # Run in subprocess
        result = subprocess.run(
            [sys.executable, 'isolated_auth.py'],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        output = result.stdout.strip()
        print(f"[*] Subprocess output: {output}")
        
        if output.startswith('SUCCESS:'):
            master_token = output.split(':', 1)[1]
            print(f"[✓] SUCCESS! Master token: {master_token}")
            return master_token, None, android_id
        
        # Clean up
        if os.path.exists('isolated_auth.py'):
            os.remove('isolated_auth.py')
        
        return None, None, android_id
        
    except Exception as e:
        print(f"[!] Method 3 failed: {e}")
        return None, None, None

def save_tokens(master_token, access_token, android_id):
    """Save tokens to file"""
    tokens = {
        "master_token": master_token,
        "access_token": access_token,
        "username": USERNAME,
        "android_id": android_id,
        "timestamp": time.time(),
        "method": "final_android_auth"
    }
    
    with open('google_tokens.json', 'w') as f:
        json.dump(tokens, f, indent=2)
    
    print(f"[✓] Tokens saved to google_tokens.json")

def main():
    """Main authentication function"""
    print("Final Android Master Token Authentication")
    print("=" * 50)
    print(f"Email: {USERNAME}")
    print(f"Password: {'*' * len(PASSWORD)}")
    print("=" * 50)
    
    # Setup environment
    setup_ssl_environment()
    
    # Install dependencies
    if not install_working_dependencies():
        print("[!] Failed to install dependencies")
        return
    
    # Try methods in order of reliability
    methods = [
        method_1_gpsoauth_with_ssl_fix,
        method_2_direct_requests,
        method_3_subprocess_isolation
    ]
    
    for i, method in enumerate(methods, 1):
        try:
            master_token, access_token, android_id = method()
            
            if master_token:
                print(f"\n{'='*50}")
                print("🎉 AUTHENTICATION SUCCESSFUL! 🎉")
                print(f"{'='*50}")
                print(f"Master Token: {master_token}")
                if access_token:
                    print(f"Access Token: {access_token}")
                print(f"Android ID: {android_id}")
                print(f"Method Used: {i}")
                print(f"{'='*50}")
                
                # Verify token format
                if master_token.startswith('aas_et/'):
                    print("[✓] Token format verified (starts with 'aas_et/')")
                else:
                    print(f"[!] Warning: Unexpected token format")
                
                # Save tokens
                save_tokens(master_token, access_token, android_id)
                
                print("\n[*] Authentication completed successfully!")
                print("You can now use this master token for Android authentication.")
                return
                
        except Exception as e:
            print(f"[!] Method {i} exception: {e}")
            continue
    
    # All methods failed
    print(f"\n{'='*50}")
    print("❌ ALL METHODS FAILED")
    print(f"{'='*50}")
    print("Possible issues:")
    print("1. App password is incorrect or expired")
    print("2. Account has additional security restrictions")
    print("3. Google has changed authentication requirements")
    print("4. Network/firewall issues")
    print()
    print("SOLUTIONS:")
    print("1. Generate a NEW app-specific password:")
    print("   https://myaccount.google.com/apppasswords")
    print("2. Ensure 2FA is enabled on your Google account")
    print("3. Check account security settings")
    print("4. Try from a different network/location")
    print(f"{'='*50}")

if __name__ == "__main__":
    # Suppress SSL warnings
    try:
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    except:
        pass
    
    main()
