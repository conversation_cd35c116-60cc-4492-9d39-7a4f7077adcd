#!/usr/bin/env python3
"""
Android Master Token Authentication Script
==========================================

This script provides multiple methods to authenticate with Google services and retrieve
Android Master Tokens (starting with 'aas_et/') using modern, working approaches.

Requirements:
- Python 3.9+
- Valid Google account with app-specific password
- Internet connection

Author: AI Assistant
License: MIT
"""

import os
import sys
import subprocess
import urllib.request
import ssl
from uuid import getnode as getmac
import json
import time
import hashlib
from typing import Optional, Dict, Any

# Configuration
DEBUG = True
REQUIRED_PACKAGES = [
    "glocaltokens>=0.7.6",
    "gpsoauth==1.0.2", 
    "urllib3<2.0.0",
    "requests>=2.25.0"
]

class AndroidMasterTokenAuth:
    """Main class for Android Master Token authentication"""
    
    def __init__(self, username: str, password: str, android_id: Optional[str] = None):
        """
        Initialize the authentication client
        
        Args:
            username: Google account email
            password: Google app-specific password (recommended) or account password
            android_id: Optional Android device ID (will be generated if not provided)
        """
        self.username = username
        self.password = password
        self.android_id = android_id or self._generate_android_id()
        self.master_token = None
        self.access_token = None
        
        # Setup SSL certificate handling
        self._setup_ssl_certificates()
        
    def _setup_ssl_certificates(self):
        """Download and setup SSL certificates to avoid SSL verification errors"""
        try:
            cert_path = os.path.join(os.getcwd(), 'cacert.pem')
            if not os.path.exists(cert_path):
                print("[*] Downloading SSL certificates...")
                urllib.request.urlretrieve('https://curl.se/ca/cacert.pem', cert_path)
            
            # Set environment variable for SSL certificate bundle
            os.environ['CURL_CA_BUNDLE'] = cert_path
            os.environ['REQUESTS_CA_BUNDLE'] = cert_path
            
            if DEBUG:
                print(f"[*] SSL certificates configured: {cert_path}")
                
        except Exception as e:
            print(f"[!] Warning: Could not setup SSL certificates: {e}")
    
    def _generate_android_id(self) -> str:
        """Generate a unique Android device ID based on MAC address"""
        try:
            mac_int = getmac()
            if (mac_int >> 40) % 2:
                # Generate a random-like ID if MAC is not available
                # Use a more realistic Android ID generation
                seed = f"{self.username}_{time.time()}"
                hash_obj = hashlib.md5(seed.encode())
                mac_int = int(hash_obj.hexdigest()[:12], 16)

            android_id = self._create_mac_string(mac_int).replace(':', '')
            if DEBUG:
                print(f"[*] Generated Android ID: {android_id}")
            return android_id

        except Exception as e:
            print(f"[!] Error generating Android ID: {e}")
            # Fallback to a realistic static ID that looks like a real Android device
            return "38400000deadbeef"
    
    def _create_mac_string(self, num: int, splitter: str = ':') -> str:
        """Convert MAC address integer to string format"""
        mac = hex(num)[2:]
        if mac.endswith('L'):
            mac = mac[:-1]
        pad = max(12 - len(mac), 0)
        mac = '0' * pad + mac
        mac = splitter.join([mac[x:x + 2] for x in range(0, 12, 2)])
        return mac.upper()
    
    def authenticate_with_glocaltokens(self) -> Optional[str]:
        """
        Method 1: Use glocaltokens library (most reliable as of 2024/2025)
        """
        try:
            print("\n[*] Attempting authentication with glocaltokens library...")
            
            from glocaltokens.client import GLocalAuthenticationTokens
            
            client = GLocalAuthenticationTokens(
                username=self.username,
                password=self.password
            )
            
            # Get master token
            master_token = client.get_master_token()
            
            if master_token and master_token.startswith('aas_et/'):
                print(f"[✓] Successfully obtained master token via glocaltokens")
                if DEBUG:
                    print(f"[*] Master token: {master_token[:20]}...")
                
                # Also get access token for verification
                try:
                    access_token = client.get_access_token()
                    if access_token:
                        self.access_token = access_token
                        print(f"[✓] Access token also obtained (expires in 1 hour)")
                except Exception as e:
                    print(f"[!] Warning: Could not get access token: {e}")
                
                self.master_token = master_token
                return master_token
            else:
                print(f"[!] Invalid master token format received: {master_token}")
                return None
                
        except ImportError:
            print("[!] glocaltokens library not available")
            return None
        except Exception as e:
            print(f"[!] glocaltokens authentication failed: {e}")
            return None
    
    def authenticate_with_gpsoauth(self) -> Optional[str]:
        """
        Method 2: Use gpsoauth library with proper version constraints
        """
        try:
            print("\n[*] Attempting authentication with gpsoauth library...")
            
            from gpsoauth import perform_master_login, perform_oauth
            
            # Perform master login
            res = perform_master_login(self.username, self.password, self.android_id)
            
            if DEBUG:
                print(f"[*] gpsoauth response: {res}")
            
            if 'Token' in res:
                master_token = res['Token']
                
                if master_token and master_token.startswith('aas_et/'):
                    print(f"[✓] Successfully obtained master token via gpsoauth")
                    if DEBUG:
                        print(f"[*] Master token: {master_token[:20]}...")
                    
                    # Try to get access token
                    try:
                        oauth_res = perform_oauth(
                            self.username, 
                            master_token, 
                            self.android_id,
                            app='com.google.android.apps.chromecast.app',
                            service='oauth2:https://www.google.com/accounts/OAuthLogin',
                            client_sig='24bb24c05e47e0aefa68a58a766179d9b613a600'
                        )
                        
                        if 'Auth' in oauth_res:
                            self.access_token = oauth_res['Auth']
                            print(f"[✓] Access token also obtained (expires in 1 hour)")
                            
                    except Exception as e:
                        print(f"[!] Warning: Could not get access token: {e}")
                    
                    self.master_token = master_token
                    return master_token
                else:
                    print(f"[!] Invalid master token format: {master_token}")
                    return None
            else:
                error = res.get('Error', 'Unknown error')
                print(f"[!] gpsoauth authentication failed: {error}")
                
                if error == 'NeedsBrowser':
                    print("[!] Browser authentication required. Try:")
                    print("    1. Use an app-specific password instead of your main password")
                    print("    2. Visit: https://myaccount.google.com/apppasswords")
                    print("    3. Generate a new app password for 'Mail'")
                elif error == 'BadAuthentication':
                    print("[!] Invalid credentials. Check your username and password.")
                
                return None
                
        except ImportError:
            print("[!] gpsoauth library not available")
            return None
        except Exception as e:
            print(f"[!] gpsoauth authentication failed: {e}")
            return None
    
    def get_master_token(self) -> Optional[str]:
        """
        Get Android Master Token using available methods
        
        Returns:
            Master token string starting with 'aas_et/' or None if failed
        """
        print(f"\n{'='*60}")
        print("Android Master Token Authentication")
        print(f"{'='*60}")
        print(f"Username: {self.username}")
        print(f"Android ID: {self.android_id}")
        print(f"{'='*60}")
        
        # Method 1: Try glocaltokens (most reliable)
        token = self.authenticate_with_glocaltokens()
        if token:
            return token
        
        # Method 2: Try gpsoauth as fallback
        token = self.authenticate_with_gpsoauth()
        if token:
            return token
        
        print("\n[!] All authentication methods failed!")
        print("\nTroubleshooting tips:")
        print("1. Ensure you're using an app-specific password:")
        print("   https://myaccount.google.com/apppasswords")
        print("2. Check that 2FA is enabled on your Google account")
        print("3. Verify your username (full email) and password are correct")
        print("4. Try running the script again after a few minutes")
        
        return None
    
    def save_tokens(self, filename: str = "google_tokens.json"):
        """Save tokens to a JSON file for later use"""
        if self.master_token:
            tokens = {
                "master_token": self.master_token,
                "access_token": self.access_token,
                "username": self.username,
                "android_id": self.android_id,
                "timestamp": time.time()
            }
            
            with open(filename, 'w') as f:
                json.dump(tokens, f, indent=2)
            
            print(f"[✓] Tokens saved to {filename}")
        else:
            print("[!] No tokens to save")


def install_dependencies():
    """Install required Python packages"""
    print("[*] Installing required dependencies...")
    
    for package in REQUIRED_PACKAGES:
        try:
            print(f"[*] Installing {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
        except subprocess.CalledProcessError as e:
            print(f"[!] Failed to install {package}: {e}")
            return False
    
    print("[✓] All dependencies installed successfully")
    return True


def main():
    """Main function"""
    print("Android Master Token Authentication Script")
    print("=========================================")

    # Install dependencies
    if not install_dependencies():
        print("[!] Failed to install dependencies. Exiting.")
        return

    # Use provided credentials for testing
    username = "<EMAIL>"
    password = "bppjpvwspocrwhqs"

    print(f"\nUsing provided credentials:")
    print(f"Email: {username}")
    print(f"App Password: {'*' * len(password)}")

    if not username or not password:
        print("[!] Username and password are required!")
        return
    
    # Create authentication client
    auth_client = AndroidMasterTokenAuth(username, password)
    
    # Get master token
    master_token = auth_client.get_master_token()
    
    if master_token:
        print(f"\n{'='*60}")
        print("SUCCESS!")
        print(f"{'='*60}")
        print(f"Master Token: {master_token}")
        if auth_client.access_token:
            print(f"Access Token: {auth_client.access_token}")
        print(f"Android ID: {auth_client.android_id}")
        print(f"{'='*60}")
        
        # Save tokens
        auth_client.save_tokens()
        
        print("\nIMPORTANT NOTES:")
        print("- Master tokens are long-lived but may expire")
        print("- Access tokens expire after 1 hour")
        print("- Store the master token securely")
        print("- Don't share these tokens with anyone")
        
    else:
        print(f"\n{'='*60}")
        print("FAILED!")
        print(f"{'='*60}")
        print("Could not obtain master token. Please check the troubleshooting tips above.")


if __name__ == "__main__":
    main()
