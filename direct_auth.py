#!/usr/bin/env python3
"""
Direct Android Authentication
============================

This script uses direct HTTP requests to Google's authentication endpoints
with proper Android device simulation and headers.
"""

import requests
import urllib.parse
import hashlib
import time
import json
import os
from uuid import getnode as getmac

# Configuration
USERNAME = '<EMAIL>'
PASSWORD = 'bppjpvwspocrwhqs'
DEBUG = True

def generate_android_id():
    """Generate a realistic Android device ID"""
    try:
        # Use MAC address as base
        mac_int = getmac()
        if (mac_int >> 40) % 2:
            # Fallback to deterministic generation
            seed = f"{USERNAME}_android_device"
            hash_obj = hashlib.md5(seed.encode())
            mac_int = int(hash_obj.hexdigest()[:12], 16)
        
        # Convert to Android ID format
        android_id = hex(mac_int)[2:].upper().zfill(16)
        return android_id
    except:
        # Ultimate fallback
        return "38400000DEADBEEF"

def setup_session():
    """Setup requests session with proper SSL and headers"""
    session = requests.Session()
    
    # Setup SSL certificate
    cert_path = os.path.join(os.getcwd(), 'cacert.pem')
    if os.path.exists(cert_path):
        session.verify = cert_path
    
    # Set common headers for Android device
    session.headers.update({
        'User-Agent': 'GoogleAuth/1.4 (generic_x86 KK)',
        'Accept-Encoding': 'gzip',
        'Connection': 'Keep-Alive'
    })
    
    return session

def get_master_token_direct(username, password, android_id):
    """Get master token using direct HTTP requests"""
    print(f"[*] Attempting direct authentication for {username}")
    print(f"[*] Using Android ID: {android_id}")
    
    session = setup_session()
    
    # Prepare authentication data
    auth_data = {
        'accountType': 'HOSTED_OR_GOOGLE',
        'Email': username,
        'Passwd': password,
        'service': 'ac2dm',
        'source': 'android',
        'androidId': android_id,
        'device_country': 'us',
        'operatorCountry': 'us',
        'lang': 'en',
        'sdk_version': '17',
        'client_sig': '38918a453d07199354f8b19af05ec6562ced5788',
        'callerSig': '38918a453d07199354f8b19af05ec6562ced5788'
    }
    
    try:
        # Make the authentication request
        response = session.post(
            'https://android.clients.google.com/auth',
            data=auth_data,
            timeout=30
        )
        
        if DEBUG:
            print(f"[*] Response status: {response.status_code}")
            print(f"[*] Response headers: {dict(response.headers)}")
            print(f"[*] Response text: {response.text[:200]}...")
        
        # Parse response
        result = {}
        for line in response.text.split('\n'):
            if '=' in line:
                key, value = line.split('=', 1)
                result[key] = value
        
        if 'Auth' in result:
            token = result['Auth']
            if token.startswith('aas_et/'):
                print(f"[✓] Successfully obtained master token!")
                return token
            else:
                print(f"[!] Invalid token format: {token}")
                return None
        elif 'Error' in result:
            error = result['Error']
            print(f"[!] Authentication error: {error}")
            
            if error == 'BadAuthentication':
                print("[!] Invalid credentials. Possible issues:")
                print("    1. App password is incorrect")
                print("    2. 2FA is not enabled")
                print("    3. Account has security restrictions")
            elif error == 'NeedsBrowser':
                print("[!] Browser authentication required")
                print("    Generate a new app-specific password")
            
            return None
        else:
            print(f"[!] Unexpected response: {result}")
            return None
            
    except Exception as e:
        print(f"[!] Request failed: {e}")
        return None

def get_access_token_direct(username, master_token, android_id):
    """Get access token using master token"""
    print("[*] Getting access token...")
    
    session = setup_session()
    
    oauth_data = {
        'accountType': 'HOSTED_OR_GOOGLE',
        'Email': username,
        'EncryptedPasswd': master_token,
        'service': 'oauth2:https://www.google.com/accounts/OAuthLogin',
        'source': 'android',
        'androidId': android_id,
        'app': 'com.google.android.apps.chromecast.app',
        'client_sig': '24bb24c05e47e0aefa68a58a766179d9b613a600',
        'device_country': 'us',
        'operatorCountry': 'us',
        'lang': 'en',
        'sdk_version': '17'
    }
    
    try:
        response = session.post(
            'https://android.clients.google.com/auth',
            data=oauth_data,
            timeout=30
        )
        
        result = {}
        for line in response.text.split('\n'):
            if '=' in line:
                key, value = line.split('=', 1)
                result[key] = value
        
        if 'Auth' in result:
            print("[✓] Access token obtained!")
            return result['Auth']
        else:
            print(f"[!] Could not get access token: {result.get('Error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"[!] Access token request failed: {e}")
        return None

def try_alternative_credentials():
    """Try with slightly modified credentials"""
    print("\n[*] Trying alternative credential formats...")
    
    alternatives = [
        # Try without spaces in password (in case there are any)
        PASSWORD.replace(' ', ''),
        # Try lowercase email
        USERNAME.lower(),
    ]
    
    android_id = generate_android_id()
    
    for i, alt_cred in enumerate(alternatives):
        print(f"[*] Trying alternative {i+1}...")
        
        if '@' in alt_cred:
            # It's an email alternative
            token = get_master_token_direct(alt_cred, PASSWORD, android_id)
        else:
            # It's a password alternative
            token = get_master_token_direct(USERNAME, alt_cred, android_id)
        
        if token:
            return token, android_id
    
    return None, android_id

def save_tokens(master_token, access_token, android_id):
    """Save tokens to JSON file"""
    tokens = {
        "master_token": master_token,
        "access_token": access_token,
        "username": USERNAME,
        "android_id": android_id,
        "timestamp": time.time()
    }
    
    with open('google_tokens.json', 'w') as f:
        json.dump(tokens, f, indent=2)
    
    print("[✓] Tokens saved to google_tokens.json")

def main():
    """Main authentication function"""
    print("Direct Android Authentication")
    print("=" * 40)
    
    # Generate Android ID
    android_id = generate_android_id()
    print(f"[*] Generated Android ID: {android_id}")
    
    print(f"""
Authentication Details:
- Email: {USERNAME}
- App Password: {'*' * len(PASSWORD)}
- Android ID: {android_id}
""")
    
    # Try primary authentication
    print("[*] Attempting primary authentication...")
    master_token = get_master_token_direct(USERNAME, PASSWORD, android_id)
    
    if not master_token:
        print("\n[!] Primary authentication failed. Trying alternatives...")
        master_token, android_id = try_alternative_credentials()
    
    if not master_token:
        print("\n[!] All authentication attempts failed!")
        print("\nPossible solutions:")
        print("1. Verify the app password is correct (no spaces)")
        print("2. Generate a new app-specific password:")
        print("   https://myaccount.google.com/apppasswords")
        print("3. Ensure 2FA is enabled on your Google account")
        print("4. Check if the account has any security restrictions")
        print("5. Try waiting a few minutes and running again")
        return
    
    print(f"\n[✓] Master token obtained: {master_token}")
    
    # Try to get access token
    access_token = get_access_token_direct(USERNAME, master_token, android_id)
    
    if access_token:
        print(f"[✓] Access token: {access_token}")
    else:
        print("[!] Could not get access token (master token should still work)")
        access_token = None
    
    # Save tokens
    save_tokens(master_token, access_token, android_id)
    
    print("\n" + "=" * 50)
    print("SUCCESS!")
    print("=" * 50)
    print(f"Master Token: {master_token}")
    if access_token:
        print(f"Access Token: {access_token}")
    print(f"Android ID: {android_id}")
    print("=" * 50)
    
    # Verify token format
    if master_token.startswith('aas_et/'):
        print("\n[✓] Master token format is correct (starts with 'aas_et/')")
    else:
        print(f"\n[!] Warning: Master token doesn't start with 'aas_et/': {master_token[:20]}...")
    
    print("\n[*] Authentication completed successfully!")

if __name__ == "__main__":
    main()
