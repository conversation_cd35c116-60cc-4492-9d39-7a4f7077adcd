#!/usr/bin/env python3
"""
Manual Test Script for Android Master Token
"""

import subprocess
import sys

# Install exact working versions
print("Installing exact working versions...")
subprocess.check_call([sys.executable, "-m", "pip", "install", "gpsoauth==1.0.2", "--force-reinstall"])
subprocess.check_call([sys.executable, "-m", "pip", "install", "urllib3==1.25.1", "--force-reinstall"])

from gpsoauth import perform_master_login
import hashlib

USERNAME = "<EMAIL>"
PASSWORD = "bppjpvwspocrwhqs"

# Generate Android ID
seed = f"{USERNAME}_android_device"
hash_obj = hashlib.md5(seed.encode())
android_id = hash_obj.hexdigest()[:16].upper()

print(f"Username: {USERNAME}")
print(f"Password: {'*' * len(PASSWORD)}")
print(f"Android ID: {android_id}")

print("\nAttempting authentication...")
result = perform_master_login(USERNAME, PASSWORD, android_id)
print(f"Result: {result}")

if 'Token' in result:
    token = result['Token']
    print(f"\nSUCCESS!")
    print(f"Master Token: {token}")
    
    # Save to file
    import json
    with open('manual_tokens.json', 'w') as f:
        json.dump({"master_token": token, "android_id": android_id}, f, indent=2)
    print("\nTokens saved to manual_tokens.json")
else:
    print(f"\nFAILED: {result.get('Error', 'Unknown error')}")
