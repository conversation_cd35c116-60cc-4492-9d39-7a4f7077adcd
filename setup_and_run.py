#!/usr/bin/env python3
"""
Setup and Run Script for Android Master Token Authentication
============================================================

This script handles the complete setup and execution of the Android Master Token
authentication process, including dependency management and environment setup.

Usage:
    python setup_and_run.py

Author: AI Assistant
License: MIT
"""

import os
import sys
import subprocess
import platform
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print(f"[!] Python 3.9+ required. Current version: {version.major}.{version.minor}")
        print("Please upgrade Python and try again.")
        return False
    
    print(f"[✓] Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_pip():
    """Check if pip is available"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "--version"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("[✓] pip is available")
        return True
    except subprocess.CalledProcessError:
        print("[!] pip is not available. Please install pip and try again.")
        return False

def install_requirements():
    """Install requirements from requirements.txt"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"[!] {requirements_file} not found!")
        return False
    
    print(f"[*] Installing requirements from {requirements_file}...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", requirements_file, "--upgrade"
        ])
        print("[✓] Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[!] Failed to install requirements: {e}")
        return False

def verify_installation():
    """Verify that required packages are installed correctly"""
    required_packages = ["glocaltokens", "gpsoauth", "urllib3", "requests"]
    
    print("[*] Verifying package installation...")
    
    for package in required_packages:
        try:
            spec = importlib.util.find_spec(package)
            if spec is None:
                print(f"[!] Package {package} not found")
                return False
            else:
                print(f"[✓] {package} is available")
        except ImportError:
            print(f"[!] Failed to import {package}")
            return False
    
    return True

def run_main_script():
    """Run the main authentication script"""
    main_script = "android_master_token_auth.py"
    
    if not os.path.exists(main_script):
        print(f"[!] {main_script} not found!")
        return False
    
    print(f"[*] Running {main_script}...")
    
    try:
        subprocess.check_call([sys.executable, main_script])
        return True
    except subprocess.CalledProcessError as e:
        print(f"[!] Script execution failed: {e}")
        return False
    except KeyboardInterrupt:
        print("\n[!] Script interrupted by user")
        return False

def main():
    """Main setup and execution function"""
    print("Android Master Token Authentication - Setup & Run")
    print("=" * 55)
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.machine()}")
    print("=" * 55)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Check pip availability
    if not check_pip():
        return 1
    
    # Install requirements
    if not install_requirements():
        print("\n[!] Setup failed during requirements installation")
        return 1
    
    # Verify installation
    if not verify_installation():
        print("\n[!] Setup failed during package verification")
        return 1
    
    print("\n[✓] Setup completed successfully!")
    print("=" * 55)
    
    # Run main script
    if run_main_script():
        print("\n[✓] Script completed successfully!")
        return 0
    else:
        print("\n[!] Script execution failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
