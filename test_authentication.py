#!/usr/bin/env python3
"""
Test Script for Android Master Token Authentication
==================================================

This script provides testing and validation functionality for the authentication system.

Usage:
    python test_authentication.py

Author: AI Assistant
License: MIT
"""

import os
import sys
import json
import time
from typing import Dict, Any, Optional

def test_token_format(token: str) -> bool:
    """Test if a token has the correct format"""
    if not token:
        return False
    
    # Master token should start with 'aas_et/'
    if token.startswith('aas_et/'):
        print(f"[✓] Master token format is correct")
        return True
    
    # Access token should start with 'ya29.'
    elif token.startswith('ya29.'):
        print(f"[✓] Access token format is correct")
        return True
    
    else:
        print(f"[!] Invalid token format: {token[:20]}...")
        return False

def test_saved_tokens(filename: str = "google_tokens.json") -> bool:
    """Test saved tokens from JSON file"""
    if not os.path.exists(filename):
        print(f"[!] Token file {filename} not found")
        return False
    
    try:
        with open(filename, 'r') as f:
            tokens = json.load(f)
        
        print(f"[*] Testing tokens from {filename}")
        
        # Check required fields
        required_fields = ['master_token', 'username', 'android_id', 'timestamp']
        for field in required_fields:
            if field not in tokens:
                print(f"[!] Missing required field: {field}")
                return False
            print(f"[✓] Field '{field}' present")
        
        # Test master token format
        master_token = tokens.get('master_token')
        if not test_token_format(master_token):
            return False
        
        # Test access token if present
        access_token = tokens.get('access_token')
        if access_token:
            if not test_token_format(access_token):
                print("[!] Warning: Access token format invalid")
        
        # Check token age
        timestamp = tokens.get('timestamp', 0)
        age_hours = (time.time() - timestamp) / 3600
        print(f"[*] Tokens are {age_hours:.1f} hours old")
        
        if age_hours > 24:
            print("[!] Warning: Tokens are more than 24 hours old, may need refresh")
        
        print(f"[✓] Token file validation passed")
        return True
        
    except json.JSONDecodeError as e:
        print(f"[!] Invalid JSON in token file: {e}")
        return False
    except Exception as e:
        print(f"[!] Error reading token file: {e}")
        return False

def test_dependencies() -> bool:
    """Test if all required dependencies are available"""
    dependencies = [
        'glocaltokens',
        'gpsoauth', 
        'urllib3',
        'requests'
    ]
    
    print("[*] Testing dependencies...")
    
    all_available = True
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"[✓] {dep} is available")
        except ImportError:
            print(f"[!] {dep} is not available")
            all_available = False
    
    return all_available

def test_ssl_setup() -> bool:
    """Test SSL certificate setup"""
    cert_file = "cacert.pem"
    
    if os.path.exists(cert_file):
        print(f"[✓] SSL certificate file found: {cert_file}")
        
        # Check file size (should be substantial)
        size = os.path.getsize(cert_file)
        if size > 100000:  # Should be > 100KB
            print(f"[✓] SSL certificate file size looks good: {size} bytes")
            return True
        else:
            print(f"[!] SSL certificate file seems too small: {size} bytes")
            return False
    else:
        print(f"[!] SSL certificate file not found: {cert_file}")
        return False

def run_basic_test() -> bool:
    """Run basic functionality test without authentication"""
    print("[*] Running basic functionality test...")
    
    try:
        # Test Android ID generation
        from android_master_token_auth import AndroidMasterTokenAuth
        
        # Create instance without credentials for testing
        auth = AndroidMasterTokenAuth("<EMAIL>", "test_password")
        
        if auth.android_id and len(auth.android_id) > 10:
            print(f"[✓] Android ID generation works: {auth.android_id}")
        else:
            print(f"[!] Android ID generation failed: {auth.android_id}")
            return False
        
        print("[✓] Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"[!] Basic functionality test failed: {e}")
        return False

def main():
    """Main test function"""
    print("Android Master Token Authentication - Test Suite")
    print("=" * 55)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("SSL Setup", test_ssl_setup),
        ("Basic Functionality", run_basic_test),
        ("Saved Tokens", test_saved_tokens)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[*] Running {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"[✓] {test_name} test PASSED")
            else:
                print(f"[!] {test_name} test FAILED")
        except Exception as e:
            print(f"[!] {test_name} test ERROR: {e}")
    
    print(f"\n{'=' * 55}")
    print(f"Test Results: {passed}/{total} tests passed")
    print(f"{'=' * 55}")
    
    if passed == total:
        print("[✓] All tests passed! System is ready.")
        return 0
    else:
        print(f"[!] {total - passed} test(s) failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
