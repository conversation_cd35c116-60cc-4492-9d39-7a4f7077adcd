#!/usr/bin/env python3
"""
Enhanced Android Master Token Authentication
============================================

This script uses the exact working method from the GitHub gist with enhanced
Android device simulation and multiple fallback approaches.

Based on: https://gist.github.com/rithvikvibhu/952f83ea656c6782fbd0f1645059055d
"""

import os
import sys
import subprocess
import urllib.request
import ssl
import time
import hashlib
import json
from uuid import getnode as getmac

# User credentials
USERNAME = '<EMAIL>'
PASSWORD = 'bppjpvwspocrwhqs'

# Optional Overrides (Set to None to ignore)
device_id = None
master_token = None
access_token = None

# Flags
DEBUG = True

def install_exact_dependencies():
    """Install exact working versions of dependencies"""
    print("[*] Installing exact working dependencies...")
    
    # Uninstall conflicting versions first
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "gpsoauth", "-y"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    except:
        pass
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "uninstall", "urllib3", "-y"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    except:
        pass
    
    # Install exact working versions
    packages = [
        "urllib3==1.25.1",
        "gpsoauth==1.0.2",
        "requests>=2.25.0"
    ]
    
    for package in packages:
        try:
            print(f"[*] Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError as e:
            print(f"[!] Failed to install {package}: {e}")
            return False
    
    print("[✓] Dependencies installed successfully")
    return True

def setup_ssl_certificates():
    """Setup SSL certificates to avoid verification errors"""
    try:
        cert_path = os.path.join(os.getcwd(), 'cacert.pem')
        if not os.path.exists(cert_path):
            print("[*] Downloading SSL certificates...")
            urllib.request.urlretrieve('https://curl.se/ca/cacert.pem', cert_path)
        
        # Set environment variable for SSL certificate bundle
        os.environ['CURL_CA_BUNDLE'] = cert_path
        os.environ['REQUESTS_CA_BUNDLE'] = cert_path
        
        print(f"[*] SSL certificates configured: {cert_path}")
        return True
        
    except Exception as e:
        print(f"[!] Warning: Could not setup SSL certificates: {e}")
        return False

def get_master_token(username, password, android_id):
    """Get master token using gpsoauth"""
    try:
        from gpsoauth import perform_master_login
        
        print(f"[*] Attempting master login for {username}")
        print(f"[*] Using Android ID: {android_id}")
        
        res = perform_master_login(username, password, android_id)
        
        if DEBUG:
            print(f"[*] Response: {res}")
        
        if 'Token' not in res:
            error = res.get('Error', 'Unknown error')
            print(f'[!] Could not get master token. Error: {error}')
            
            if error == 'NeedsBrowser':
                print("[!] Browser authentication required.")
                print("    Try using an app-specific password instead.")
                print("    Generate one at: https://myaccount.google.com/apppasswords")
            elif error == 'BadAuthentication':
                print("[!] Invalid credentials.")
                print("    1. Verify your email and app password are correct")
                print("    2. Ensure 2FA is enabled on your Google account")
                print("    3. Generate a new app-specific password")
            
            return None
        
        token = res['Token']
        if token and token.startswith('aas_et/'):
            print(f"[✓] Successfully obtained master token!")
            return token
        else:
            print(f"[!] Invalid token format: {token}")
            return None
            
    except ImportError:
        print("[!] gpsoauth library not available")
        return None
    except Exception as e:
        print(f"[!] Error during master login: {e}")
        return None

def get_access_token(username, master_token, android_id):
    """Get access token using master token"""
    try:
        from gpsoauth import perform_oauth
        
        res = perform_oauth(
            username, master_token, android_id,
            app='com.google.android.apps.chromecast.app',
            service='oauth2:https://www.google.com/accounts/OAuthLogin',
            client_sig='24bb24c05e47e0aefa68a58a766179d9b613a600'
        )
        
        if DEBUG:
            print(f"[*] OAuth response: {res}")
        
        if 'Auth' not in res:
            print('[!] Could not get access token.')
            return None
        
        return res['Auth']
        
    except Exception as e:
        print(f"[!] Error getting access token: {e}")
        return None

def _get_android_id():
    """Generate Android device ID"""
    try:
        mac_int = getmac()
        if (mac_int >> 40) % 2:
            # Generate a deterministic ID based on username
            seed = f"{USERNAME}_android_device"
            hash_obj = hashlib.md5(seed.encode())
            mac_int = int(hash_obj.hexdigest()[:12], 16)
        
        android_id = _create_mac_string(mac_int)
        android_id = android_id.replace(':', '')
        return android_id
        
    except Exception as e:
        print(f"[!] Error generating Android ID: {e}")
        # Fallback to a realistic static ID
        return "38400000deadbeef"

def _create_mac_string(num, splitter=':'):
    """Create MAC address string from number"""
    mac = hex(num)[2:]
    if mac[-1] == 'L':
        mac = mac[:-1]
    pad = max(12 - len(mac), 0)
    mac = '0' * pad + mac
    mac = splitter.join([mac[x:x + 2] for x in range(0, 12, 2)])
    mac = mac.upper()
    return mac

def save_tokens(master_token, access_token, android_id):
    """Save tokens to JSON file"""
    tokens = {
        "master_token": master_token,
        "access_token": access_token,
        "username": USERNAME,
        "android_id": android_id,
        "timestamp": time.time()
    }
    
    filename = "google_tokens.json"
    with open(filename, 'w') as f:
        json.dump(tokens, f, indent=2)
    
    print(f"[✓] Tokens saved to {filename}")

def try_alternative_method():
    """Try alternative authentication method using Docker approach"""
    print("\n[*] Trying alternative Docker-based method...")
    
    # Create a temporary script with the exact working configuration
    script_content = '''
import os
import urllib.request

# Download SSL certificate
urllib.request.urlretrieve('https://curl.se/ca/cacert.pem', 'cacert.pem')
os.environ['CURL_CA_BUNDLE'] = f'{os.getcwd()}/cacert.pem'

from gpsoauth import perform_master_login, perform_oauth
from uuid import getnode as getmac

USERNAME = '<EMAIL>'
PASSWORD = 'bppjpvwspocrwhqs'

def _get_android_id():
    mac_int = getmac()
    if (mac_int >> 40) % 2:
        import hashlib
        seed = f"{USERNAME}_android_device"
        hash_obj = hashlib.md5(seed.encode())
        mac_int = int(hash_obj.hexdigest()[:12], 16)
    
    mac = hex(mac_int)[2:]
    if mac[-1] == 'L':
        mac = mac[:-1]
    pad = max(12 - len(mac), 0)
    mac = '0' * pad + mac
    mac = ':'.join([mac[x:x + 2] for x in range(0, 12, 2)])
    return mac.upper().replace(':', '')

android_id = _get_android_id()
print(f"Android ID: {android_id}")

res = perform_master_login(USERNAME, PASSWORD, android_id)
print(f"Master login result: {res}")

if 'Token' in res:
    master_token = res['Token']
    print(f"Master Token: {master_token}")
    
    # Try to get access token
    oauth_res = perform_oauth(
        USERNAME, master_token, android_id,
        app='com.google.android.apps.chromecast.app',
        service='oauth2:https://www.google.com/accounts/OAuthLogin',
        client_sig='24bb24c05e47e0aefa68a58a766179d9b613a600'
    )
    
    if 'Auth' in oauth_res:
        access_token = oauth_res['Auth']
        print(f"Access Token: {access_token}")
'''
    
    try:
        with open('temp_auth.py', 'w') as f:
            f.write(script_content)
        
        result = subprocess.run([sys.executable, 'temp_auth.py'], 
                              capture_output=True, text=True, timeout=60)
        
        print(f"[*] Alternative method output:")
        print(result.stdout)
        
        if result.stderr:
            print(f"[*] Errors: {result.stderr}")
        
        # Clean up
        if os.path.exists('temp_auth.py'):
            os.remove('temp_auth.py')
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"[!] Alternative method failed: {e}")
        return False

def main():
    """Main authentication function"""
    print("Enhanced Android Master Token Authentication")
    print("=" * 50)
    
    # Setup SSL certificates
    setup_ssl_certificates()
    
    # Install exact dependencies
    if not install_exact_dependencies():
        print("[!] Failed to install dependencies")
        return
    
    # Generate Android ID if not provided
    if not device_id:
        android_id = _get_android_id()
        print(f"[*] Generated Android ID: {android_id}")
    else:
        android_id = device_id
    
    print(f'''
This script generates tokens for Google Home Foyer API.

1. Master token - Form: aas_et/*** (long lived)
2. Access token - Form: ya29.*** (1 hour)

Using credentials:
- Email: {USERNAME}
- App Password: {'*' * len(PASSWORD)}
- Android ID: {android_id}
''')
    
    print('\n[*] Getting master token...')
    if not master_token:
        token = get_master_token(USERNAME, PASSWORD, android_id)
        if token:
            master_token_result = token
        else:
            print("\n[!] Primary method failed. Trying alternative approach...")
            if try_alternative_method():
                print("[*] Check the output above for tokens")
                return
            else:
                print("[!] All methods failed!")
                return
    else:
        master_token_result = master_token
    
    print(f'[✓] Master token: {master_token_result}')
    
    print('\n[*] Getting access token...')
    if not access_token:
        access_token_result = get_access_token(USERNAME, master_token_result, android_id)
    else:
        access_token_result = access_token
    
    if access_token_result:
        print(f'[✓] Access token: {access_token_result}')
    else:
        print('[!] Could not get access token (master token should still work)')
        access_token_result = None
    
    # Save tokens
    save_tokens(master_token_result, access_token_result, android_id)
    
    print('\n' + '=' * 50)
    print('SUCCESS!')
    print('=' * 50)
    print(f'Master Token: {master_token_result}')
    if access_token_result:
        print(f'Access Token: {access_token_result}')
    print(f'Android ID: {android_id}')
    print('=' * 50)
    print('\n[*] Done.')

if __name__ == "__main__":
    main()
